import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/delta_type.dart';
import 'package:vp_trading/core/constant/time_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class TrailingStopEditOrderDialog extends StatefulWidget {
  const TrailingStopEditOrderDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<TrailingStopEditOrderDialog> createState() =>
      _TrailingStopEditOrderDialogState();
}

class _TrailingStopEditOrderDialogState
    extends State<TrailingStopEditOrderDialog> {
  final _volumeFocusNode = FocusNode();
  final _stepPriceFocusNode = FocusNode();
  final _rangeFocusNode = FocusNode();
  late final TextEditingController _volumeController;
  late final TextEditingController _stepPriceController;
  late final TextEditingController _rangeController;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing data
    _volumeController = TextEditingController(
      text: widget.model?.qty?.toString() ?? '',
    );
    _stepPriceController = TextEditingController(
      text: widget.model?.priceStep ?? '',
    );
    _rangeController = TextEditingController(
      text: widget.model?.deltaValue?.toString() ?? '',
    );

    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final conditionCubit =
          context.read<DerivativeValidateConditionOrderCubit>();
      final orderCubit = context.read<DerivativeValidateOrderCubit>();

      // Initialize volume
      if (widget.model?.qty != null) {
        orderCubit.onChangeVolume(widget.model!.qty!.toString());
      }

      // Initialize step price
      if (widget.model?.priceStep != null) {
        conditionCubit.onChangeStepPrice(widget.model!.priceStep!);
      }

      // Initialize range (deltaValue)
      if (widget.model?.deltaValue != null) {
        conditionCubit.onChangeRange(widget.model!.deltaValue!.toString());
      }
    });
  }

  @override
  void dispose() {
    _volumeFocusNode.dispose();
    _stepPriceFocusNode.dispose();
    _rangeFocusNode.dispose();
    _volumeController.dispose();
    _stepPriceController.dispose();
    _rangeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDialogTitle(),
        const SizedBox(height: 8),
        _buildInformationSection(),
        const SizedBox(height: 8),

        // Input Fields Section
        _buildInputFieldsWithListeners(),
        const SizedBox(height: 16),

        // Action Buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildDialogTitle() {
    return Center(
      child: Text(
        'Sửa lệnh',
        style: context.textStyle.subtitle16?.copyWith(
          color: vpColor.textPrimary,
          fontWeight: FontWeight.w700,
          fontSize: 20,
        ),
      ),
    );
  }

  Widget _buildInformationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Order Type Information
        _buildInformationRow(
          label: 'Loại lệnh',
          value: widget.model?.conditionOrderTypeFuEnum.title ?? '',
        ),
        const SizedBox(height: 8),

        // Contract Code Information
        _buildInformationRow(
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '',
        ),
      ],
    );
  }

  Widget _buildInformationRow({required String label, required String value}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsWithListeners() {
    return MultiBlocListener(
      listeners: _buildBlocListeners(),
      child: Column(children: [_buildInputFieldsSection()]),
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      _buildVolumeListener(),
      _buildStepPriceListener(),
      _buildRangeListener(),
      _buildFocusListener(),
    ];
  }

  BlocListener _buildVolumeListener() {
    return BlocListener<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentVolume != current.currentVolume,
      listener: (context, state) {
        if (state.currentVolume != null) {
          _volumeController.text = state.currentVolume!;
          _volumeController.selection = TextSelection.fromPosition(
            TextPosition(offset: _volumeController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildStepPriceListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentStepPrice != current.currentStepPrice,
      listener: (context, state) {
        if (state.currentStepPrice != null &&
            state.currentStepPrice?.isNotEmpty == true) {
          _stepPriceController.text = state.currentStepPrice!;
          _stepPriceController.selection = TextSelection.fromPosition(
            TextPosition(offset: _stepPriceController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildRangeListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) => previous.currentRange != current.currentRange,
      listener: (context, state) {
        if (state.currentRange != null &&
            state.currentRange?.isNotEmpty == true) {
          _rangeController.text = state.currentRange!;
          _rangeController.selection = TextSelection.fromPosition(
            TextPosition(offset: _rangeController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildFocusListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.focusKeyboard != current.focusKeyboard,
      listener: (context, state) {
        if (state.focusKeyboard == FocusKeyboard.stepPrice) {
          _stepPriceFocusNode.requestFocus();
        } else if (state.focusKeyboard == FocusKeyboard.range) {
          _rangeFocusNode.requestFocus();
        }
      },
    );
  }

  Widget _buildInputFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildVolumeInput(),
        const SizedBox(height: 8),
        _buildStepPriceInput(),
        const SizedBox(height: 8),
        _buildRangeInput(),
      ],
    );
  }

  Widget _buildInputLabel(String text) {
    return Expanded(
      flex: 1,
      child: Text(
        text,
        style: context.textStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
    );
  }

  Widget _buildVolumeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [_buildVolumeInputRow(), _buildVolumeErrorDisplay()],
    );
  }

  Widget _buildVolumeInputRow() {
    return Row(
      children: [
        _buildInputLabel('Khối lượng đặt'),
        const SizedBox(width: 16),
        _buildVolumeInputField(),
      ],
    );
  }

  Widget _buildVolumeInputField() {
    return SizedBox(
      width: 160,
      child: InputFieldBox(
        controller: _volumeController,
        hintText: '5',
        onChange: (value) {
          context.read<DerivativeValidateOrderCubit>().onChangeVolume(value);
        },
        focusNode: _volumeFocusNode,
        onTap: (increase) {
          context.read<DerivativeValidateOrderCubit>().volumeTap(
            text: _volumeController.text,
            increase: increase,
          );
        },
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(8),
        ],
      ),
    );
  }

  Widget _buildStepPriceInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          children: [
            _buildInputLabel('Bước giá'),
            const SizedBox(width: 16),
            SizedBox(
              width: 160,
              child: InputFieldBox(
                controller: _stepPriceController,
                hintText: '0.1',
                onChange: (value) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .onChangeStepPrice(value);
                },
                focusNode: _stepPriceFocusNode,
                onTap: (increase) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .stepPriceTap(
                        text: _stepPriceController.text,
                        increase: increase,
                      );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...priceDerivativeInputFormatter,
                ],
              ),
            ),
          ],
        ),
        _buildStepPriceErrorDisplay(),
      ],
    );
  }

  Widget _buildRangeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          children: [
            _buildInputLabel('Biên độ'),
            const SizedBox(width: 16),
            SizedBox(
              width: 160,
              child: InputFieldBox(
                controller: _rangeController,
                hintText: '1.0',
                onChange: (value) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .onChangeRange(value);
                },
                focusNode: _rangeFocusNode,
                onTap: (increase) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .rangeTap(
                        text: _rangeController.text,
                        increase: increase,
                      );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...priceDerivativeInputFormatter,
                ],
              ),
            ),
          ],
        ),
        _buildRangeErrorDisplay(),
      ],
    );
  }

  Widget _buildVolumeErrorDisplay() {
    return Align(
      alignment: Alignment.centerRight,
      child: BlocBuilder<
        DerivativeValidateOrderCubit,
        DerivativeValidateOrderState
      >(
        buildWhen:
            (previous, current) =>
                previous.errorVolume != current.errorVolume ||
                previous.currentVolume != current.currentVolume,
        builder: (context, state) {
          final cubit = context.read<DerivativeValidateOrderCubit>();
          final maxVolumeValue = cubit.maxVolume().toString();

          return InputFieldError(
            errorMessage: state.errorVolume.message(maxVolumeValue),
            text: _volumeController.text,
            isShake: true,
            textAlign: TextAlign.end,
          );
        },
      ),
    );
  }

  Widget _buildStepPriceErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorStepPrice != current.errorStepPrice ||
              previous.currentStepPrice != current.currentStepPrice,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorStepPrice.message,
          text: _stepPriceController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildRangeErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorRange != current.errorRange ||
              previous.currentRange != current.currentRange,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorRange.message,
          text: _rangeController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      builder: (context, validateState) {
        return BlocBuilder<
          DerivativeConditionOrderEditCubit,
          DerivativeConditionOrderEditState
        >(
          builder: (context, editState) {
            final isValid = _isFormValid(validateState);

            return _buildButtonRow(isValid, editState, validateState);
          },
        );
      },
    );
  }

  bool _isFormValid(DerivativeValidateConditionOrderState validateState) {
    return BlocProvider.of<DerivativeValidateOrderCubit>(
              context,
            ).state.errorVolume.isError ==
            false &&
        !validateState.errorStepPrice.isError &&
        !validateState.errorRange.isError &&
        _stepPriceController.text.isNotEmpty &&
        _rangeController.text.isNotEmpty &&
        _volumeController.text.isNotEmpty &&
        _hasFieldsChanged();
  }

  bool _hasFieldsChanged() {
    final originalVolume = widget.model?.qty?.toString() ?? '';
    final originalStepPrice = widget.model?.priceStep ?? '';
    final originalRange = widget.model?.deltaValue?.toString() ?? '';

    final currentVolume = _volumeController.text;
    final currentStepPrice = _stepPriceController.text;
    final currentRange = _rangeController.text;

    return originalVolume != currentVolume ||
        originalStepPrice != currentStepPrice ||
        originalRange != currentRange;
  }

  Widget _buildButtonRow(
    bool isValid,
    DerivativeConditionOrderEditState editState,
    DerivativeValidateConditionOrderState validateState,
  ) {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed:
                editState.canPerformAction
                    ? () {
                      context.pop();
                    }
                    : null,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            disabled: !isValid || !editState.canPerformAction,
            onPressed:
                isValid && editState.canPerformAction
                    ? () {
                      _handleConfirm(context, validateState);
                    }
                    : null,
          ),
        ),
      ],
    );
  }

  void _handleConfirm(
    BuildContext context,
    DerivativeValidateConditionOrderState validateState,
  ) {
    if (widget.model == null) return;

    final request = ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderId: widget.model!.orderId ?? '',
      accountId: GetIt.instance<SubAccountCubit>().derivativeAccount?.id ?? "",
      orderType: widget.model!.orderType ?? '',
      conditionInfo: ConditionInfo(
        symbol: widget.model!.symbol ?? '',
        qty: int.tryParse(_volumeController.text) ?? widget.model!.qty ?? 0,
        side: widget.model!.orderTypeFUEnum.codeRequestCondition.toLowerCase(),
        type: "limit",
        fromDate: widget.model!.fromDate ?? '',
        toDate: widget.model!.toDate ?? '',
        activeType: 'C',  
        priceStep: _stepPriceController.text.priceDerivative,
        deltaValue: _rangeController.text.priceDerivative,
        deltaType: DeltaType.D.toServer, 
        timetype: TimeType.T.toServer,
      ),
    );

    context
        .read<DerivativeConditionOrderEditCubit>()
        .editDerivativeConditionOrder(request: request);
  }
}
